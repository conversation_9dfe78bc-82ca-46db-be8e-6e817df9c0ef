const { IView } = require('../../../component/iview');
const { <PERSON><PERSON><PERSON>el<PERSON> } = require('../../../libs/helper-biz');
const { AggregatedPosition } = require('./records');
const { AccountSimple } = require('../../20cm/components/objects');
const { LabelValue, SellTask, TaskObject, TaskStatus } = require('../objects');
const { Cm20FunctionCodes } = require('../../../config/20cm');
const { repoInstrument } = require('../../../repository/instrument');
const { repoAccount } = require('../../../repository/account');

/**
 * @returns {Array<LabelValue>}
 */
function MakeLevels(count) {
    var levels = [];

    // for (let idx = count; idx >= 1; idx--) {
    //     levels.push(new LabelValue('卖' + idx, -idx));
    // }

    for (let idx = 1; idx <= count; idx++) {
        levels.push(new LabelValue('买' + idx, idx));
    }

    return levels;
}

/**
 * @returns {Array<LabelValue>}
 */
function MakeBuyLevels(count) {
    var levels = [];

    for (let idx = 1; idx <= count; idx++) {
        levels.push(new LabelValue('卖' + idx, idx));
    }

    return levels;
}

/**
 * @returns {AggregatedPosition}
 */
function MakeAggregatedPosition() {
    return null;
}

/**
 * @returns {TaskObject}
 */
function MakeAutoSellTask() {
    return null;
}

module.exports = class RecordsView extends IView {
    constructor() {
        super('@20cm-july/main/trading', false, '交易');
        this.defaults = { customRatio: 20 };
        this.accounts = [new AccountSimple({})].splice(1);
        this.modes = { mannual: 1, auto: 2 };
        this.directions = { buy: 1, sell: -1 };
        this.levels = MakeLevels(5);
        this.buyLevels = MakeBuyLevels(5);

        this.strategyMap = {
            intervallySell: { label: '定时定量-卖出', value: 101 },
            intervallyBuy: { label: '定时定量-买入', value: 98 },
            lowControl: { label: '低封单量', value: 103 },
            scheduledBuy: { label: '定时买', value: 109 },
        };

        this.strategies = [this.strategyMap.intervallyBuy, this.strategyMap.scheduledBuy];

        this.tsellsMap = {};

        /**
         * 持仓请求，数据到达，回调
         */
        this.positionCallbacks = [function (position = new AggregatedPosition()) {}].splice(1);

        /**
         * 自动卖出任务，数据到达，回调
         */
        this.sellTaskCallbacks = [function (task = new SellTask({})) {}].splice(1);
    }

    get autoTsk() {
        return this.states.autoTsk;
    }

    readSetCustomRatio(ratio) {
        let key = 'manual-trade-custom-rate';
        if (typeof ratio == 'number' && ratio >= 1) {
            localStorage.setItem(key, ratio);
        }

        let recent = parseFloat(localStorage.getItem(key));
        return recent >= 1 ? recent : this.defaults.customRatio;
    }

    createApp() {
        this.states = {
            isCreditAccount: false,
            availableCash: 0,
            focused: this.modes.mannual,
            linkedPosition: MakeAggregatedPosition(),
            autoTsk: MakeAutoSellTask(),
        };

        this.mannual = {
            instrument: null,
            instrumentName: null,
            direction: this.directions.buy,
            keywords: null,
            price: 0,
            ceiling: 0,
            floor: 0,
            volume: 0,
            amount: 0,
            isByVolume: true,
            estimated: null,
            lowerSellLimit: 0,
            upperBuyLimit: 0,
            customRatio: this.readSetCustomRatio(),
            isEditingCustomRatio: false,
        };

        this.auto = {
            instrument: null,
            instrumentName: null,
            keywords: null,
            strategy: this.strategyMap.intervallyBuy.value,
            level: null,
            volume: 0,
            cancel: {
                /** 撤单保护，是否启用 */
                enabled: false,
                /** 撤单保护市场，单位秒 */
                time: null,
            },
            // 定时定量 时间间隔
            strategyDelayTime: 1,
            // 定时定量卖出 卖出仓位
            positionPercent: 100,
            // 定时定量买入 总买入量
            targetVolume: 0,
            // 定时定量 涨幅上限
            upperLimit: undefined,
            // 定时定量 涨幅下限
            lowerLimit: undefined,
            // 定时买 参数
            strategyTime: '092459',
            strategyTimeMs: 0, // 毫秒部分
            orderPrice: 0,
            orderAmount: 0,
            isByVolume: true, // 定时买是否按数量（true）还是按金额（false）
            priceType: 1, // 价格类型：1-价格，2-涨幅
            strategyRaiseRate: 0, // 涨幅百分比
        };

        const $vapp = new Vue({
            el: this.$container.firstElementChild,
            data: {
                states: this.states,
                modes: this.modes,
                directions: this.directions,
                strategies: this.strategies,
                levels: this.levels,
                buyLevels: this.buyLevels,
                mannual: this.mannual,
                auto: this.auto,
            },
            computed: {
                properStep: () => {
                    return this.isLowBuy1VolumeStrategy() ? 1 : 100;
                },
            },
            watch: {
                'mannual.price': () => {
                    if (this.mannual.isByVolume) {
                        this.estimateCost(this.mannual.price, this.mannual.volume);
                    } else {
                        this.estimateVoume(this.mannual.price, this.mannual.amount);
                    }
                },

                'mannual.volume': () => {
                    this.estimateCost(this.mannual.price, this.mannual.volume);
                },

                'mannual.amount': () => {
                    this.estimateVoume(this.mannual.price, this.mannual.amount);
                },
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.handleModeChange,
                this.handleDirectionChange,
                this.isMannualMode,
                this.isAutoMode,
                this.isMannualBuy,

                this.handleSuggest,
                this.handleInput,
                this.handleSelect,
                this.handleClear,

                this.setAsPrice,
                this.setByRatio,
                this.setByCustomRatio,
                this.mbuy,
                this.mcredit,
                this.msell,
                this.handleStart,
                this.stopAuto,
                this.saveAuto,
                this.isAutoRunning,
                this.isAsNewTask,
                this.getPositionCondition,
                this.getAccountAvailableCondition,
                this.isLowBuy1VolumeStrategy,
                this.isIntervalVolumeSell,
                this.isIntervalVolumeBuy,
                this.isScheduledBuy,
                this.getAutoLabel,
                this.calculateByPrice,
                this.calculateByVolume,
                this.calculateByAmount,
                this.toggleMethod,
                this.precisePrice,
                this.decidePriceStep,
                this.handleCustomRatioChange,
                this.handleCustomRatioBlur,
                this.change2Volume,
                this.change2Amount,
                this.change2RaiseRate,
                this.change2Price
            ]),
        });
    }

    change2Volume() {
        this.auto.isByVolume = true;
        this.auto.orderAmount = 0;
    }

    change2Amount() {
        this.auto.isByVolume = false;
        this.auto.volume = 0;
    }

    change2RaiseRate() {
        this.auto.orderPrice = 0;
        this.auto.priceType = 2;
    }

    change2Price() {
        this.auto.strategyRaiseRate = 0;
        this.auto.priceType = 1;
    }

    precisePrice(price) {
        return typeof price == 'number' ? price.toFixed(this.getPricePrecision()) : price;
    }

    decidePriceStep() {
        return this.getPricePrecision() == 3 ? 0.001 : 0.01;
    }

    getPricePrecision() {
        let instrument = this.isMannualMode() ? this.mannual.instrument : this.auto.instrument;
        return BizHelper.getPricePrecision(instrument);
    }

    calculateByPrice($event) {
        let price = parseFloat($event.target.value);
        if (this.mannual.isByVolume) {
            this.estimateCost(price, this.mannual.volume);
        } else {
            this.estimateVoume(price, this.mannual.amount);
        }
    }

    calculateByVolume($event) {
        this.estimateCost(this.mannual.price, parseFloat($event.target.value));
    }

    calculateByAmount($event) {
        this.estimateVoume(this.mannual.price, parseFloat($event.target.value));
    }

    estimateCost(price, volume) {
        if (price > 0 && volume > 0) {
            let amount = price * volume;
            let result;

            if (amount < 10000) {
                result = amount.toFixed(0);
            } else if (amount < 100000) {
                result = (amount / 10000).toFixed(1) + '万';
            } else if (amount < 100000000) {
                result = (amount / 10000).toFixed(0) + '万';
            } else {
                result = (amount / 100000000).toFixed(2) + '亿';
            }

            this.mannual.estimated = `预估 ${result}`;
        } else {
            this.mannual.estimated = null;
        }
    }

    transferAmount2Volume(price, amount) {
        return price > 0 && amount > 0 ? Math.floor(amount / price / 100) * 100 : 0;
    }

    estimateVoume(price, amount) {
        if (price > 0 && amount > 0) {
            let volume = this.transferAmount2Volume(price, amount);
            let result;

            if (volume < 10000) {
                result = volume + '股';
            } else {
                result = volume / 100 + '手';
            }

            this.mannual.estimated = `预估 ${result}`;
        } else {
            this.mannual.estimated = null;
        }
    }

    toggleMethod() {
        if (this.isMannualSell()) {
            return;
        }

        const ref = this.mannual;
        ref.isByVolume = !ref.isByVolume;

        if (ref.isByVolume) {
            this.estimateCost(ref.price, ref.volume);
        } else {
            this.estimateVoume(ref.price, ref.amount);
        }
    }

    /**
     * @param {TaskObject} task
     */
    handleSellTask(task) {
        this.states.focused = this.modes.auto;
        this.handleClear();
        setTimeout(() => {
            this.setAsInstrument(task.instrument, task.instrumentName);
        }, 20);
    }

    handleModeChange() {
        this.handleClear();
    }

    handleDirectionChange() {
        this.mannual.isByVolume = true;
    }

    isMannualMode() {
        return this.states.focused == this.modes.mannual;
    }

    isAutoMode() {
        return this.states.focused == this.modes.auto;
    }

    isMannualBuy() {
        return this.isMannualMode() && this.mannual.direction == this.directions.buy;
    }

    isMannualSell() {
        return this.isMannualMode() && this.mannual.direction == this.directions.sell;
    }

    handleInput() {
        const ref = this.isMannualMode() ? this.mannual : this.auto;

        if (event.keyCode == 8) {
            event.returnValue = false;
            ref.keywords = null;
            this.handleClear();
        } else if (typeof ref.keywords == 'string' && ref.keywords.trim().length == 0) {
            this.handleClear();
        }
    }

    handleClear() {
        console.log('clear');

        this.linkPosition(null);
        this.setAsInstrument(null, null);
        this.trigger('set-instrument', null, null);
    }

    /**
     * @param {String} keywords
     * @param {Function} callback
     */
    handleSuggest(keywords, callback) {
        if (typeof keywords != 'string' || keywords.trim().length < 1) {
            callback([]);
            return;
        }

        let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, keywords);
        if (matches.length == 1) {
            callback([]);
            this.handleSelect(matches[0]);
            return;
        }

        callback(matches);
    }

    handleSelect(selected) {
        var { instrument, instrumentName } = selected;
        this.setAsInstrument(instrument, instrumentName);
        this.trigger('set-instrument', instrument, instrumentName);
    }

    /**
     * @param {String} instrument
     */
    shortizeCode(instrument) {
        return instrument.split('.')[1];
    }

    async setAsInstrument(instrument, instrumentName) {
        this.log(`set stock to: ${instrument}/${instrumentName}, mode = ${this.states.focused}`);
        var keywords = instrument ? `${this.shortizeCode(instrument)}-${instrumentName}` : null;

        if (this.isMannualMode()) {
            let ref = this.mannual;
            ref.keywords = keywords;
            ref.instrument = instrument;
            ref.instrumentName = instrumentName;
        } else {
            let ref = this.auto;
            ref.keywords = keywords;
            ref.instrument = instrument;
            ref.instrumentName = instrumentName;
            ref.strategy = this.strategyMap.intervallyBuy.value;
            ref.level = this.levels[0].value;
            ref.volume = 0;
            ref.cancel.enabled = false;
            ref.cancel.time = null;
        }

        let result = await this.requestPriceInfo(instrument);
        let ref2 = this.mannual;
        this.setAsPrice(result.lastPrice);
        ref2.ceiling = result.upperLimitPrice;
        ref2.floor = result.lowerLimitPrice;

        if (!instrument) {
            ref2.upperBuyLimit = 0;
            ref2.lowerSellLimit = 0;
        } else {
            ref2.upperBuyLimit = Math.min(ref2.ceiling, ref2.upperBuyLimit);
            ref2.lowerSellLimit = Math.max(ref2.floor, ref2.lowerSellLimit);
        }

        ref2.volume = 0;
        this.sendPositionRequest(instrument);

        if (this.isAutoMode()) {
            this.link2Task(instrument);
        }
    }

    sendPositionRequest(instrument, callback) {
        this.log(`send a position request from trading panel, mode = ${this.states.focused}`);
        typeof callback == 'function' && this.positionCallbacks.push(callback);
        this.trigger('request-position', instrument);
    }

    setAsPrice(value) {
        this.log(`set price in trading panel: ${value}`);
        this.mannual.price = this.precisePrice(value) || 0;
    }

    setByRatio(shares) {
        this.handleRatioChange(shares);
    }

    setByCustomRatio(ratio) {
        this.handleRatioChange(100 / ratio);
    }

    handleCustomRatioChange() {
        let ratio = this.mannual.customRatio;
        let defaultv = this.defaults.customRatio;

        if (typeof ratio != 'number' || isNaN(ratio) || ratio < 1) {
            ratio = this.mannual.customRatio = defaultv;
        }

        this.readSetCustomRatio(ratio);
        this.mannual.isEditingCustomRatio = false;
        this.handleRatioChange(100 / ratio);
    }

    handleCustomRatioBlur() {
        this.mannual.isEditingCustomRatio = false;
    }

    handleRatioChange(shares) {
        if (this.isMannualBuy()) {
            if (!this.hasAnyAccount()) {
                return this.interaction.showError('账号信息此刻未获得，请稍后重试');
            } else if (!(this.mannual.price > 0)) {
                return this.interaction.showError('请输入买入价格，再点击比例');
            }

            /** 总可用资金 */
            let available = this.firstAccount.available;
            /** 扣除印花税 */
            let tax = (available * 1) / 1000;
            /** 实际可用 */
            let actural = available - tax;
            /** 可使用净金额 */
            // let netAmount = Math.floor((available - tax) / shares);
            let expectedAmount = this.firstAccount.balance / shares;
            let netAmount = expectedAmount > actural ? actural : expectedAmount;

            /**
             * 使用金额反算数量
             */

            if (this.mannual.isByVolume) {
                let { price, lowerSellLimit, upperBuyLimit, floor, ceiling } = this.mannual;
                // let uprice = true ? price : this.isMannualBuy() ? Math.min(upperBuyLimit, ceiling) : Math.max(lowerSellLimit, floor);
                // let decided = parseInt(netAmount / uprice / 100) * 100;
                let decided = parseInt(netAmount / price / 100) * 100;
                this.mannual.volume = Math.max(100, decided);
            } else {
                /**
                 * 直接设置金额
                 */
                this.mannual.amount = Math.floor(netAmount);
            }

            this.log(`click a cash percentage to buy, manual = ${JSON.stringify(this.mannual)}`);
        } else {
            let ref = this.states.linkedPosition;

            if (this.hasLinkedPosition() && ref.instrument == this.mannual.instrument) {
                let closable = ref.closableVolume;
                this.mannual.volume = shares == 1 ? closable : closable <= 100 ? closable : parseInt(closable / shares / 100) * 100;
                this.log(`click a position percentage to sell, manual = ${JSON.stringify(this.mannual)}`);
            } else {
                this.interaction.showError('仓位信息，不明确，请手动输入数量');
            }
        }
    }

    /**
     * @param {AggregatedPosition} position
     */
    linkPosition(position) {
        this.states.linkedPosition = position ? this.helper.deepClone(position) : null;
    }

    hasLinkedPosition() {
        return !!this.states.linkedPosition;
    }

    getPositionCondition() {
        var lines = [
            { label: '合约', value: this.mannual.instrumentName || 'N/A' },
            { label: '总仓位', value: 0 },
            { label: '可平仓位', value: 0 },
        ];

        if (this.hasLinkedPosition()) {
            let ref = this.states.linkedPosition;
            lines[0].value = ref.instrumentName;
            lines[1].value = ref.totalPosition.thousands();
            lines[2].value = ref.closableVolume.thousands();
        }

        return lines.map((x) => `<span style="line-height:18px;">${x.label}: ${x.value}</span>`).join('<br>');
    }

    getAccountAvailableCondition() {
        return `可用现金：${(this.states.availableCash || 0).thousands()}`;
    }

    isLowBuy1VolumeStrategy() {
        return this.auto.strategy == this.strategyMap.lowControl.value;
    }

    isIntervalVolumeSell() {
        return this.auto.strategy == this.strategyMap.intervallySell.value;
    }

    isIntervalVolumeBuy() {
        return this.auto.strategy == this.strategyMap.intervallyBuy.value;
    }

    isScheduledBuy() {
        return this.auto.strategy == this.strategyMap.scheduledBuy.value;
    }

    /**
     * @param {AggregatedPosition} position
     */
    hotUpdatePosition(position) {
        this.linkPosition(position);
    }

    /**
     * @param {AggregatedPosition} position
     */
    setPositionByRequest(position) {
        this.linkPosition(position);

        while (this.positionCallbacks.length > 0) {
            try {
                this.positionCallbacks.shift()();
            } catch (ex) {
                console.error(ex);
            }
        }
    }

    /**
     * @param {AggregatedPosition} position
     */
    setPositionByExternal(position) {
        /**
         * 当前合约所关联的持仓信息
         */
        this.linkPosition(position);

        /**
         * 设置合约信息
         */
        this.setAsInstrument(position.instrument, position.instrumentName);

        /**
         * 设置除合约以外的，其它输入项
         */

        if (this.isMannualMode()) {
            this.mannual.direction = this.directions.sell;
            this.mannual.isByVolume = true;
        } else {
            // todo22
            if (this.isAutoStopped()) {
                this.auto.level = this.levels[0].value;
            }
        }
    }

    /**
     * @param {TaskObject} task
     * @returns {TaskObject}
     */
    typeds(task) {
        return task;
    }

    link2Task(instrument) {
        var tsk = (this.states.autoTsk = this.typeds(this.tsellsMap[instrument]));
        if (!tsk) {
            return;
        }

        /**
         * 自动卖出交易：如果当前交易面板的合约，有匹配的对应自动卖出策略，则策略的参数，反向写入面板
         */

        var ref = this.auto;
        if (ref.instrument != tsk.instrument) {
            return;
        }

        // console.log(111,ref, tsk);

        ref.strategy = tsk.boardStrategy.strategyType;
        ref.level = tsk.priceFollowType;
        ref.volume = tsk.boardStrategy.strategyVolume;
        ref.cancel.enabled = !!tsk.cancelCondition.cancelProtectedEnabled;
        ref.cancel.time = tsk.cancelCondition.cancelProtectedTime;
        ref.lowerLimit = tsk.boardStrategy.lowerLimit;
        ref.upperLimit = tsk.boardStrategy.upperLimit;
        ref.strategyDelayTime = tsk.boardStrategy.strategyDelayTime / 1000;
        ref.positionPercent = tsk.positionPercent;
        ref.targetVolume = tsk.targetVolume;
        ref.orderPrice = tsk.orderPrice;
        ref.orderAmount = tsk.boardStrategy.strategyAmount;
        ref.isByVolume = ref.volume > 0;
        ref.strategyRaiseRate = tsk.boardStrategy.strategyRaiseRate || 0;
        // 根据数据判断价格类型：如果有orderPrice则为价格模式，否则为涨幅模式
        ref.priceType = tsk.orderPrice > 0 ? 1 : 2;
        // 兼容老数据，从strategyTime(HHmmssSSS)中提取时分秒和毫秒部分
        const strategyTimeStr = String(tsk.boardStrategy.strategyTime || '');
        if (strategyTimeStr.length <= 6) {
            ref.strategyTime = strategyTimeStr.padStart(6, '0');
            ref.strategyTimeMs = 0;
        } else {
            ref.strategyTime = strategyTimeStr.slice(0, -3).padStart(6, '0');
            ref.strategyTimeMs = Number(strategyTimeStr.slice(-3));
        }
    }

    /**
     * @param {Array<TaskObject>} tasks
     */
    push(tasks) {
        // this.tsellsMap = {};
        tasks.forEach((tsk) => {
            this.tsellsMap[tsk.instrument] = tsk;
        });
        let tsk = tasks[0];
        if (tsk) {
            if (this.auto.instrument == tsk.instrument) {
                this.link2Task(this.auto.instrument);
            }
        }
    }

    validateMannualParams() {
        var ref = this.mannual;
        var pi = this.priceInfo;
        var lp = this.states.linkedPosition;

        if (!this.hasAnyAccount()) {
            return '没有可用于交易的账号';
        }

        if (!ref.instrument) {
            return '合约未指定';
        }

        if (!(ref.price > 0)) {
            return '价格，未有效设置';
        }

        if (pi && pi.instrument == ref.instrument && !(ref.price >= pi.lowerLimitPrice && ref.price <= pi.upperLimitPrice)) {
            return `价格，超出涨跌停价格：${pi.lowerLimitPrice} ~ ${pi.upperLimitPrice}`;
        }

        let isByAmount = !this.mannual.isByVolume;
        if (isByAmount && this.transferAmount2Volume(ref.price, ref.amount) < 100) {
            return '金额，至少需要能够买入100股';
        }

        if (isByAmount && ref.amount > this.states.availableCash) {
            return `金额 ${ref.amount.thousands()} > 可用现金 ${(this.states.availableCash || 0).thousands()}`;
        }

        if (this.mannual.isByVolume && !(ref.volume > 0)) {
            return '数量，未有效设置';
        }

        if (this.isMannualSell() && this.hasLinkedPosition() && lp.instrument == ref.instrument && ref.volume > lp.closableVolume) {
            return `数量，超出最大可平数量 = ${lp.closableVolume.thousands()}`;
        }
    }

    mbuy() {
        this.mtrade();
    }

    mcredit() {
        this.mtrade(true);
    }

    msell() {
        this.mtrade();
    }

    mtrade(is_credit_buy = false) {
        let instrument = this.mannual.instrument;
        if (!instrument) {
            return this.interaction.showError('交易合约缺失');
        }

        this.sendPositionRequest(instrument, () => {
            this.mtradeExec(is_credit_buy);
        });
    }

    mtradeExec(is_credit_buy = false) {
        var error = this.validateMannualParams();
        if (error) {
            this.interaction.showError(error);
            return;
        }

        var ref = this.mannual;
        var isBuy = ref.direction == this.directions.buy;
        var firstAcnt = this.firstAccount;
        var cvolume = this.isMannualSell() || this.mannual.isByVolume ? ref.volume : this.transferAmount2Volume(ref.price, ref.amount);
        var direction_text = isBuy ? (is_credit_buy ? '融资买入' : '限价买入') : '限价卖出';
        var mentions = [
            ['账号', firstAcnt.accountName],
            ['合约', ref.instrument + '/' + ref.instrumentName],
            ['方向', direction_text, isBuy ? 's-color-red' : 's-color-green'],
            ['价格', ref.price],
            ['数量', cvolume.thousands()],
            ['金额', (ref.price * cvolume).thousands()],
        ];

        var message = mentions.map((item) => `<div><span>${item[0]}：</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');
        var order = {
            accountId: firstAcnt.accountId,
            strategyId: firstAcnt.fundId,
            userId: this.userInfo.userId,
            instrument: ref.instrument,
            volume: cvolume,
            price: ref.price,
            priceType: this.systemTrdEnum.pricingType.fixedPrice.code,
            bsFlag: ref.direction,
            businessFlag: is_credit_buy ? this.systemTrdEnum.businessFlag.credit.code : 0,
            positionEffect: 0,
            orderTime: new Date().getTime(),
            hedgeFlag: this.systemTrdEnum.hedgeFlag.Speculate.code,
            customId: '20cm-july-mannual-' + new Date().getTime(),
        };

        this.confirm(true, message, () => {
            console.log('to send out an order', order);
            this.placeMannualOrder(order);
            this.interaction.showSuccess('手动订单已发送');
        });
    }

    /**
     * @param {{ instrument: string, volume: number }} order
     */
    placeMannualOrder(order) {
        let ins = order.instrument;
        let max_volume = 100 * (ins.indexOf('.688') > 0 ? 1000 : ins.indexOf('.3') > 0 ? 3000 : 10000);
        let target_volume = order.volume;
        let waterflow_no = 0;

        while (target_volume > 0) {
            let current = Object.assign({}, order);
            current.volume = Math.min(max_volume, target_volume);
            current.customId = `${current.customId}-${waterflow_no++}`;
            target_volume -= current.volume;
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendOrder, current);
            this.log(`to send out a manual order: ${JSON.stringify(current)}`);
        }

        setTimeout(() => {
            this.requestAccounts();
            this.sendPositionRequest(this.mannual.instrument);
        }, 1000);
    }

    isAutoRunning() {
        const ref = this.states.autoTsk;
        return ref && ref.instrument == this.auto.instrument && TaskObject.isTaskRunning(ref.strikeBoardStatus);
    }

    getAutoLabel() {
        return this.isIntervalVolumeBuy() || this.isScheduledBuy() ? '买入' : '卖出';
    }

    isAutoStopped() {
        return !this.isAutoRunning();
    }

    isAutoCheckedOk() {
        var ref = this.auto;
        var message = null;

        if (!ref.instrument) {
            message = '合约未指定';
        } else if (this.helper.isNone(ref.strategy)) {
            message = '策略未指定';
        } else if (this.helper.isNone(ref.level)) {
            message = '档位未指定';
        } else if (this.isLowBuy1VolumeStrategy()) {
            if (this.helper.isNone(ref.positionPercent)) {
                message = '仓位比例未指定';
            } else if (ref.positionPercent < 1) {
                message = '仓位比例为1~100%';
            }
        } else if (this.isIntervalVolumeSell() || this.isIntervalVolumeBuy()) {
            if (this.helper.isNone(ref.strategyDelayTime)) {
                message = '间隔未指定';
            } else if (!ref.volume) {
                message = '数量为100的整数倍';
            }
            if (this.isIntervalVolumeBuy()) {
                if (ref.targetVolume < ref.volume) {
                    message = '总买入量不能小于单笔数量';
                }
            }
        } else if (this.isScheduledBuy()) {
            if (this.helper.isNone(ref.strategyTime)) {
                message = '买入时间未指定';
            } else if (ref.priceType === 1 && !(ref.orderPrice > 0)) {
                message = '价格未指定';
            } else if (ref.priceType === 2 && (ref.strategyRaiseRate === undefined || ref.strategyRaiseRate === null)) {
                message = '涨幅未指定';
            } else if (ref.isByVolume && !(ref.volume > 0)) {
                message = '数量未指定';
            } else if (!ref.isByVolume && !(ref.orderAmount > 0)) {
                message = '金额未指定';
            }
        }

        if (message) {
            this.interaction.showError(message);
        }

        return !message;
    }

    stopAuto() {
        if (this.isAutoStopped()) {
            return;
        }

        var ref = this.auto;
        this.confirm(false, `${ref.instrumentName}，停止监控？`, () => {
            this.log(`to stop an auto sell task, task id: ${this.autoTsk.id}, auto: ${JSON.stringify(ref)}`);
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.stop, { id: this.autoTsk.id });
            this.interaction.showSuccess('自动卖出，停止请求已发出');
        });
    }

    isAsNewTask() {
        const ref = this.autoTsk;
        if (!ref) {
            return true;
        } else if (this.auto.instrument != ref.instrument) {
            return true;
        }

        const status = ref.strikeBoardStatus;
        return TaskObject.isTaskFinished(status) || TaskObject.isTaskDeleted(status);
    }

    saveAuto() {
        if (!this.isAutoCheckedOk()) {
            return;
        }

        // 如果是新任务，则新建任务，如果是已保存的，则修改任务参数
        let is_new = this.isAsNewTask();
        if (is_new) {
            let ref = this.autoTsk;
            this.log(`to create an auto task: ${JSON.stringify(ref)}`);
            this.submitAuto(Cm20FunctionCodes.request.createSell, null, TaskStatus.created.value);
            this.interaction.showSuccess(`自动${this.getAutoLabel()}，保存请求已发出`);
        } else {
            var ref = this.autoTsk;
            this.log(`auto task param changed: ${JSON.stringify(ref)}`);
            this.submitAuto(Cm20FunctionCodes.request.modify, ref.id, ref.strikeBoardStatus);
            this.interaction.showSuccess('变动已提交');
        }
    }

    handleStart() {
        this.startAuto();
    }

    startAuto() {
        if (!this.isAutoCheckedOk()) {
            return;
        } else if (this.isAutoRunning()) {
            return this.interaction.showError(`${this.auto.instrumentName}，自动${this.getAutoLabel()}，已在执行中`);
        }
        // 如果是卖出算法并且卖出的合约没有可以持仓则提示
        if (!this.isIntervalVolumeBuy() && !this.isScheduledBuy()) {
            var ref = this.states.linkedPosition;
            if (!ref || ref.closableVolume <= 0) {
                return this.interaction.showError(`${this.auto.instrumentName}，无可卖持仓`);
            }
        }

        this.confirm(false, `${this.auto.instrument}，开启自动${this.getAutoLabel()} ?`, () => {
            let ref = this.autoTsk;
            let is_new = this.isAsNewTask();
            this.log(`to start an auto task: ${JSON.stringify(ref)}`);
            this.submitAuto(Cm20FunctionCodes.request.start, is_new ? null : ref.id, is_new ? TaskStatus.created.value : ref.strikeBoardStatus);
            this.interaction.showSuccess(`自动${this.getAutoLabel()}，启动请求已发出`);
        });
    }

    submitAuto(cmdCode, task_id, task_status) {
        var ref = this.auto;
        var zero = 0;
        var board_strategy = {
            strategyType: ref.strategy,
            strategyVolumeOpen: false,
            strategyVolume: ref.volume,
            strategyRateOpen: false,
            strategyRate: zero,
            strategyDelayTime: ref.strategy == this.strategyMap.lowControl.value ? zero : ref.strategyDelayTime * 1000,
            sellAmountOpen: false,
            sellAmount: zero,
            sellVolumeOpen: false,
            sellVolume: zero,
            upperLimit: ref.upperLimit,
            lowerLimit: ref.lowerLimit,
            // 定时买特殊参数
            strategyTime: this.isScheduledBuy() ? this.formatStrategyTimeWithMs(ref.strategyTime, ref.strategyTimeMs) : undefined,
            strategyAmount: this.isScheduledBuy() ? (!ref.isByVolume ? ref.orderAmount : undefined) : undefined,
            strategyRaiseRate: this.isScheduledBuy() ? ref.strategyRaiseRate : undefined,
        };

        var isLow = ref.positionPercent !== undefined;
        var task = new TaskObject({
            id: task_id,
            userId: this.userInfo.userId,
            userName: this.userInfo.userName,
            instrument: ref.instrument,
            instrumentName: null,
            direction: ref.strategy == this.strategyMap.intervallyBuy.value || ref.strategy == this.strategyMap.scheduledBuy.value ? this.directions.buy : this.directions.sell,
            priceFollowType: ref.level,
            orderPrice: this.isScheduledBuy() ? (ref.priceType === 1 ? ref.orderPrice : zero) : zero,
            targetVolume: ref.targetVolume,
            limitPositionType: isLow ? 6 : 1,
            positionPercent: ref.positionPercent,
            strikeBoardStatus: task_status,
            supplementVolume: zero,
            supplementOpen: false,
            splitInterval: zero,
            splitType: 1,
            splitDetail: { enableMax: false, max: 0, decline: false },
            boardStrategy: board_strategy,
            cancelCondition: this.isLowBuy1VolumeStrategy()
                ? null
                : {
                      cancelProtectedEnabled: !!ref.cancel.enabled,
                      cancelProtectedTime: ref.cancel.time,
                  },
            cash: zero,
            creditFlag: false,
        });

        console.log('to start a task', cmdCode, task);
        this.log(`to submit auto task to server: ${cmdCode}/${JSON.stringify(task)}`);
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, cmdCode, task);
    }

    confirm(isRequired, message, callback) {
        if (isRequired) {
            this.interaction.showConfirm({
                title: '操作确认',
                message: message,
                confirmed: () => {
                    callback();
                },
            });
        } else {
            callback();
        }
    }

    async requestPriceInfo(instrument) {
        var output = {
            instrument,
            preClosePrice: 0,
            lastPrice: 0,
            upperLimitPrice: 0,
            lowerLimitPrice: 0,
        };
        if (!instrument) {
            return output;
        }

        var resp = await repoInstrument.queryPrice(instrument);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};

        if (errorCode == 0 && data && upperLimitPrice > 0) {
            output.preClosePrice = preClosePrice;
            output.lastPrice = lastPrice;
            output.upperLimitPrice = upperLimitPrice;
            output.lowerLimitPrice = lowerLimitPrice;
        }

        return (this.priceInfo = output);
    }

    hasAnyAccount() {
        return this.accounts.length > 0;
    }

    get firstAccount() {
        return this.accounts[0];
    }

    async requestAccounts() {
        if (this.isRequestingAccount) {
            return;
        }

        try {
            this.isRequestingAccount = true;
            var resp = await repoAccount.getAccountDetailInfo({ userId: this.userInfo.userId });
            var { errorCode, errorMsg, data } = resp;
            var records = (data || {}).list || [];
            var accounts = errorCode == 0 ? AccountSimple.Convert(records) : [];
            this.accounts.refill(accounts);
            // 获取到账号后，设置账号是否为信用账号
            this.states.isCreditAccount = accounts.length > 0 && !!accounts[0].credit;
            // 获取到账号后，设置账号可用现金
            this.states.availableCash = accounts.length > 0 ? Math.floor(accounts[0].available) : 0;
        } catch (ex) {
            console.error(ex);
        } finally {
            this.isRequestingAccount = false;
        }
    }

    /**
     * @param {String} message
     */
    log(message) {
        this.loggerTrading.info('ztlog:' + message);
    }

    /**
     * 格式化策略时间，将时分秒和毫秒合并为HHmmssSSS格式
     * @param {string} timeStr - HHmmss格式的时间字符串
     * @param {number} ms - 毫秒数
     * @returns {number} - HHmmssSSS格式的数字
     */
    formatStrategyTimeWithMs(timeStr, ms) {
        if (!timeStr) return undefined;

        const msStr = String(ms || 0).padStart(3, '0');
        return Number(timeStr + msStr);
    }

    build($container) {
        super.build($container);
        this.createApp();
        this.requestAccounts();
        setInterval(() => {
            this.requestAccounts();
        }, 1000 * 20);
    }
};
